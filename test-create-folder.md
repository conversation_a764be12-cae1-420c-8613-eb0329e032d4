# 新建文件夹功能测试

## 测试步骤

### 1. 启动应用程序
- 运行 `pnpm dev` 启动开发服务器
- 应用程序应该在 http://localhost:5174/ 正常启动

### 2. 导航到文件夹页面
- 登录应用程序
- 导航到任意文件夹页面（如：/folders/某个分类）

### 3. 测试新建文件夹按钮
- 在工具栏中找到"新建文件夹"按钮
- 点击按钮，应该弹出新建文件夹对话框

### 4. 测试对话框功能
- 对话框应该显示：
  - 标题："新建文件夹"
  - 描述："请输入文件夹名称"
  - 文件夹名称输入框
  - 创建位置信息（显示当前所在文件夹）
  - 取消和创建按钮

### 5. 测试表单验证
- 尝试提交空的文件夹名称 → 应该显示"文件夹名称不能为空"
- 尝试输入过长的名称（>255字符） → 应该显示长度限制错误
- 尝试输入非法字符（如 `<>:"/\|?*`） → 应该显示非法字符错误
- 尝试输入以点开头或结尾的名称 → 应该显示相应错误
- 尝试输入系统保留名称（如 CON, PRN） → 应该显示保留名称错误

### 6. 测试正常创建流程
- 输入有效的文件夹名称（如："测试文件夹"）
- 点击"创建"按钮
- 应该显示加载状态（"创建中..."）
- 成功后应该：
  - 显示成功提示消息
  - 关闭对话框
  - 刷新文件夹列表
  - 新创建的文件夹应该出现在列表中

### 7. 测试取消功能
- 打开新建文件夹对话框
- 输入一些内容
- 点击"取消"按钮或关闭按钮
- 对话框应该关闭，表单应该重置

### 8. 测试键盘交互
- 打开对话框后，输入框应该自动获得焦点
- 在输入框中按 Enter 键应该触发创建操作
- 按 Escape 键应该关闭对话框

## 预期结果

### API 调用
- 应该调用 `POST /netdisk/createDirectory` 接口
- 请求参数应该包含：
  ```json
  {
    "category_id": "当前分类ID",
    "parent_id": "当前父级文件夹ID", 
    "folder_name": "用户输入的文件夹名称"
  }
  ```

### 成功响应处理
- 当 API 返回 `{code: 0}` 时，应该显示成功提示
- 应该自动刷新文件夹列表
- 新文件夹应该出现在当前目录中

### 错误响应处理
- 当 API 返回非 0 的 code 时，应该显示错误提示
- 对话框应该保持打开状态，允许用户重试

## 注意事项

1. 这是一个 Electron 应用程序，不应该在浏览器中测试
2. 需要确保后端 API 服务正常运行
3. 需要有效的认证 token
4. 测试时应该在不同的文件夹层级进行测试，确保 parent_id 正确传递

## 已实现的组件

- ✅ `src/api/services/files.ts` - API 函数
- ✅ `src/composables/useCreateFolder.ts` - 业务逻辑 composable
- ✅ `src/components/CreateFolderDialog.vue` - 对话框组件
- ✅ `src/views/Folders/FolderView.vue` - 集成到主页面
- ✅ `src/views/Folders/ToolsBar/index.vue` - 按钮触发（已存在）
