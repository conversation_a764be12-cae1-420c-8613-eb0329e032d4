import { request } from "@/api/http";
import type { ApiResponse } from "@/api/http";

interface IUploadTaskData {
  upload_url: string;
  relative_path: string;
  category_id: number | string;
  parent_id: number | string;
  [key: string]: any;
}

interface IFileOperationData {
  category_id: number | string;
  file_ids: number | string | (number | string)[]; // 支持单个ID或ID数组
}

interface IFileMoveData extends IFileOperationData {
  target_dir_id: number | string;
}

interface IFileRenameData {
  category_id: number | string;
  file_id: number | string;
  new_name: string;
}

// 下载任务接口（用于文件夹下载）
export interface DownloadTaskItem {
  id: number;
  file_name: string;
  is_folder: 0 | 1;
  relative_path: string;
  file_size: number;
  file_sign: string;
}

export interface DownloadTasksResponse {
  tasks: DownloadTaskItem[];
}

interface IFileEditData {
  category_id: number | string;
  file_id: number | string;
  [key: string]: any;
}

interface ICreateFolderData {
  category_id: number | string;
  parent_id: number | string;
  folder_name: string;
}

// 开始上传任务，将已上传的文件与后端文件目录同步
function startTask(data: IUploadTaskData): Promise<ApiResponse> {
  return request.post("/netdisk/startTask", data);
}

// 删除文件
function deleteFile(data: IFileOperationData): Promise<ApiResponse> {
  return request.post(`/netdisk/deleteFile`, data);
}

// 移动文件
function moveFiles(data: IFileMoveData): Promise<ApiResponse> {
  return request.post(`/netdisk/moveFiles`, data);
}

// 重命名文件
function renameFile(data: IFileRenameData): Promise<ApiResponse> {
  return request.post(`/netdisk/renameFile`, data);
}

// 编辑文件信息
function editFileInfo(data: IFileEditData): Promise<ApiResponse> {
  return request.post(`/netdisk/editFileInfo`, data);
}

// 获取文件夹下载任务列表（包含 relative_path）
function getDownloadTasks(category_id: number | string, id: number | string): Promise<ApiResponse<DownloadTasksResponse>> {
  return request.get("/netdisk/getDownloadTasks", {
    params: {
      category_id,
      id,
    },
  });
}

// 新建文件夹
function createFolder(data: ICreateFolderData): Promise<ApiResponse> {
  return request.post(`/netdisk/createDirectory`, data);
}

export default {
  startTask,
  deleteFile,
  moveFiles,
  getDownloadTasks,
  renameFile,
  editFileInfo,
  createFolder,
};

export type { IUploadTaskData, IFileOperationData, IFileMoveData, IFileRenameData, IFileEditData };
