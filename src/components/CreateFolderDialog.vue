<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>新建文件夹</DialogTitle>
        <DialogDescription>
          请输入文件夹名称
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- 文件夹名称输入 -->
        <div class="space-y-2">
          <Label for="folder-name">文件夹名称</Label>
          <Input id="folder-name" ref="folderNameInputRef" v-model="folderName" placeholder="请输入文件夹名称..."
            :disabled="isCreating" @keydown.enter="handleConfirm" @input="handleInput" />
          <!-- 验证错误提示 -->
          <div v-if="validationError" class="text-sm text-destructive">
            {{ validationError }}
          </div>
        </div>

        <!-- 创建位置信息 -->
        <div v-if="parentFolderName" class="p-3 rounded-lg bg-muted">
          <div class="mb-1 text-sm text-muted-foreground">创建位置</div>
          <div class="font-medium">{{ parentFolderName }}</div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="handleCancel" :disabled="isCreating">
          取消
        </Button>
        <Button @click="handleConfirm" :disabled="!canConfirm || isCreating">
          <Loader2 v-if="isCreating" class="mr-2 w-4 h-4 animate-spin" />
          {{ isCreating ? '创建中...' : '创建' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Loader2 } from 'lucide-vue-next'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateFolder } from '@/composables/useCreateFolder'

// Props
const props = defineProps<{
  /** 对话框是否打开 */
  open: boolean
  /** 分类ID */
  categoryId: string | number
  /** 父级文件夹ID */
  parentId: string | number
  /** 父级文件夹名称（用于显示创建位置） */
  parentFolderName?: string
}>()

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  confirm: [folderName: string]
  cancel: []
}>()

// 使用文件夹创建 composable
const { isCreating, validateFolderName, createFolderWithToast } = useCreateFolder()

// 状态
const folderName = ref('')
const validationError = ref('')
const folderNameInputRef = ref<HTMLInputElement>()

// 计算属性
const canConfirm = computed(() => {
  return folderName.value.trim() && !validationError.value
})

// 处理对话框开关
const handleOpenChange = (value: boolean) => {
  emit('update:open', value)
  if (!value) {
    handleCancel()
  }
}

// 处理输入变化
const handleInput = () => {
  const validation = validateFolderName(folderName.value)
  validationError.value = validation.valid ? '' : (validation.message || '')
}

// 确认创建
const handleConfirm = async () => {
  if (!canConfirm.value || isCreating.value) return

  // 最终验证
  const validation = validateFolderName(folderName.value)
  if (!validation.valid) {
    validationError.value = validation.message || '文件夹名称无效'
    return
  }

  try {
    const success = await createFolderWithToast(
      folderName.value.trim(),
      props.categoryId,
      props.parentId
    )

    if (success) {
      emit('confirm', folderName.value.trim())
      resetForm()
    }
  } catch (error) {
    console.error('创建文件夹失败:', error)
  }
}

// 取消创建
const handleCancel = () => {
  resetForm()
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  folderName.value = ''
  validationError.value = ''
}

// 监听对话框打开状态
watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    resetForm()
  }
})
</script>
